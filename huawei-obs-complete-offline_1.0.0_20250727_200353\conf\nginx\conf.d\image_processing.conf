# 华为云 OBS 图像处理服务配置 - 超简化版本
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    
    client_max_body_size 100M;
    
    # 全局代理设置
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_connect_timeout 10s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # 所有请求都代理到后端
    location / {
        proxy_pass http://127.0.0.1:8080;
    }
    
    # Nginx状态
    location /nginx_status {
        allow 127.0.0.1;
        deny all;
        stub_status on;
    }
}
