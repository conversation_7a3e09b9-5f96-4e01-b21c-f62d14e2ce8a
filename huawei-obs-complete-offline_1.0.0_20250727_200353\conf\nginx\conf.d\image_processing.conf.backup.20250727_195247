# 华为云 OBS 图像处理服务配置
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    
    # 设置最大上传大小
    client_max_body_size 100M;
    
    # 根目录重定向到健康检查
    location = / {
        return 302 /health;
    }
    
    # 健康检查接口
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
    }
    
    # 图像处理接口 - 带缓存
    location ~* ^/images/(.+)$ {
        set $image_path $1;
        
        # 缓存配置
        proxy_cache images;
        proxy_cache_valid 200 1h;
        proxy_cache_valid 404 1m;
        proxy_cache_key "$scheme$request_method$host$request_uri";
        
        # 添加缓存状态头
        add_header X-Cache-Status $upstream_cache_status;
        
        # 代理到后端图像处理服务
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Image-Path $image_path;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }
    
    # 文件上传接口
    location /upload {
        proxy_pass http://127.0.0.1:8080/upload;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 上传超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API接口 - 不缓存
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # 禁用缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # 静态文件服务
    location /static/ {
        alias /opt/huawei_obs_image_processing/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # Nginx状态页面（仅本地访问）
    location /nginx_status {
        allow 127.0.0.1;
        deny all;
        stub_status on;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        return 404 "Image not found";
    }
    
    location = /50x.html {
        internal;
        return 500 "Internal server error";
    }
}
