# 华为云 OBS 图像处理服务配置 - 标准API版本
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;
    
    # 设置最大上传大小
    client_max_body_size 100M;
    
    # 根目录重定向到健康检查
    location = / {
        proxy_pass http://127.0.0.1:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 健康检查接口
    location /health {
        proxy_pass http://127.0.0.1:8080/health;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_connect_timeout 5s;
        proxy_read_timeout 10s;
    }
    
    # 标准图像处理接口 - 支持 x-image-process 和 x-oss-process
    location ~* \.(jpg|jpeg|png|gif|webp|bmp|tiff)$ {
        # 检查是否有图像处理参数
        set $has_process 0;
        if ($args ~ "x-image-process=") {
            set $has_process 1;
        }
        if ($args ~ "x-oss-process=") {
            set $has_process 1;
        }
        
        # 如果有处理参数，代理到后端处理
        if ($has_process = 1) {
            # 缓存配置
            proxy_cache images;
            proxy_cache_valid 200 1h;
            proxy_cache_valid 404 1m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            
            # 添加缓存状态头
            add_header X-Cache-Status $upstream_cache_status;
            
            # 代理到后端图像处理服务
            proxy_pass http://127.0.0.1:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            # 超时设置
            proxy_connect_timeout 10s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # 错误处理
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        }
        
        # 如果没有处理参数，返回404或重定向到示例
        if ($has_process = 0) {
            return 302 $uri?x-image-process=image/resize,w_400,h_300;
        }
    }
    
    # 通用路径处理（支持任意路径的图像处理）
    location / {
        # 检查是否有图像处理参数
        set $has_process 0;
        if ($args ~ "x-image-process=") {
            set $has_process 1;
        }
        if ($args ~ "x-oss-process=") {
            set $has_process 1;
        }
        
        # 如果有处理参数，代理到后端处理
        if ($has_process = 1) {
            proxy_cache images;
            proxy_cache_valid 200 1h;
            proxy_cache_valid 404 1m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            
            add_header X-Cache-Status $upstream_cache_status;
            
            proxy_pass http://127.0.0.1:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            proxy_connect_timeout 10s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # 如果没有处理参数，正常代理
        if ($has_process = 0) {
            proxy_pass http://127.0.0.1:8080;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
    
    # 静态文件服务
    location /static/ {
        alias /opt/huawei_obs_image_processing/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # Nginx状态页面（仅本地访问）
    location /nginx_status {
        allow 127.0.0.1;
        deny all;
        stub_status on;
    }
}
