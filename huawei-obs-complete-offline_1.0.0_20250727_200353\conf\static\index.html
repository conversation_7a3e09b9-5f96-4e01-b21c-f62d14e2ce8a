<!DOCTYPE html>
<html>
<head>
    <title>华为云 OBS 图像处理服务</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status { color: green; font-weight: bold; font-size: 18px; }
        .api-example { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        code { background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
        .feature { margin: 10px 0; }
        .feature::before { content: "✅ "; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 华为云 OBS 图像处理服务</h1>
        <p class="status">服务状态: 运行中</p>
        
        <h2>🚀 功能特性</h2>
        <div class="feature">图像格式转换 (PNG, JPG, GIF, WebP)</div>
        <div class="feature">图像大小调整和裁剪</div>
        <div class="feature">图像质量压缩</div>
        <div class="feature">图像旋转和翻转</div>
        <div class="feature">水印添加</div>
        <div class="feature">华为云 OBS 存储集成</div>
        <div class="feature">智能缓存优化</div>
        
        <h2>📡 API 接口</h2>
        <div class="api-example">
            <strong>健康检查:</strong><br>
            <code>GET /health</code>
        </div>
        
        <div class="api-example">
            <strong>图像处理:</strong><br>
            <code>GET /images/sample.jpg?w=200&h=150&quality=80</code><br>
            <small>支持参数: w(宽度), h(高度), quality(质量), rotate(旋转), crop(裁剪), watermark(水印), format(格式)</small>
        </div>
        
        <div class="api-example">
            <strong>文件上传:</strong><br>
            <code>POST /upload</code>
        </div>
        
        <h2>🔧 使用示例</h2>
        <ul>
            <li><a href="/health" target="_blank">健康检查</a></li>
            <li><a href="/nginx_status" target="_blank">Nginx状态</a></li>
        </ul>
        
        <h2>📖 参数说明</h2>
        <ul>
            <li><strong>w, h</strong>: 宽度和高度 (像素)</li>
            <li><strong>quality</strong>: 图像质量 (1-100)</li>
            <li><strong>rotate</strong>: 旋转角度 (度)</li>
            <li><strong>crop</strong>: 裁剪 (width,height,x,y)</li>
            <li><strong>watermark</strong>: 水印文字</li>
            <li><strong>format</strong>: 输出格式 (jpg, png, gif, webp)</li>
        </ul>
    </div>
</body>
</html>
