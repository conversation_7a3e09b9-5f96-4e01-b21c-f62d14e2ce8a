[Unit]
Description=华为云 OBS 图像处理服务
After=network.target

[Service]
Type=simple
User=image-processor
Group=image-processor
ExecStart=/usr/local/bin/huawei_obs_image_processor
WorkingDirectory=/opt/huawei_obs_image_processing
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=CONFIG_FILE=/opt/huawei_obs_image_processing/conf/production.conf

[Install]
WantedBy=multi-user.target
