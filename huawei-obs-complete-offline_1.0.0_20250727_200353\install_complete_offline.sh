#!/bin/bash

# 华为云 OBS 图像处理系统 - 完全离线安装脚本
# 包含所有依赖，无需任何网络连接

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
   log_error "此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

INSTALL_DIR="/opt/huawei_obs_image_processing"
SERVICE_USER="image-processor"
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "========================================"
echo "🚀 华为云 OBS 图像处理系统"
echo "   完全离线安装（包含所有依赖）"
echo "========================================"
echo

log_step "1. 检查系统环境..."
log_info "系统版本: $(cat /etc/redhat-release)"
log_info "安装目录: $CURRENT_DIR"
log_info "模式: 完全离线（包含所有依赖）"

log_step "2. 安装RPM依赖包..."
if [ -d "$CURRENT_DIR/rpms/downloaded" ] && [ "$(ls -A $CURRENT_DIR/rpms/downloaded/*.rpm 2>/dev/null)" ]; then
    log_info "安装离线RPM包..."
    cd "$CURRENT_DIR/rpms/downloaded"
    
    # 强制安装所有RPM包
    rpm -Uvh --force --nodeps *.rpm 2>/dev/null || {
        log_warn "部分RPM包安装失败，继续使用预编译文件"
    }
    
    cd "$CURRENT_DIR"
    log_info "✓ RPM包安装完成"
else
    log_info "使用预编译文件，跳过RPM安装"
fi

log_step "3. 创建用户和目录..."
# 创建服务用户
if ! id "$SERVICE_USER" &>/dev/null; then
    useradd -r -s /bin/false -d "$INSTALL_DIR" "$SERVICE_USER" 2>/dev/null || {
        log_warn "用户创建失败，可能已存在"
    }
    log_info "✓ 服务用户处理完成: $SERVICE_USER"
fi

# 创建nginx用户
if ! id nginx >/dev/null 2>&1; then
    useradd -r -s /bin/false nginx 2>/dev/null || true
fi

# 创建目录结构
directories=(
    "$INSTALL_DIR" "$INSTALL_DIR/bin" "$INSTALL_DIR/conf" "$INSTALL_DIR/logs"
    "/var/log/image_processing" "/var/cache/image_processing" "/tmp/image_processing"
    "/usr/local/bin" "/usr/bin" "/usr/sbin" "/usr/lib64"
    "/etc/nginx" "/var/cache/nginx/images" "/var/log/nginx"
)

for dir in "${directories[@]}"; do
    mkdir -p "$dir"
done

log_info "✓ 目录结构创建完成"

log_step "4. 安装二进制程序..."
# 安装所有二进制文件
if [ -d "$CURRENT_DIR/bin" ]; then
    for binary in "$CURRENT_DIR/bin"/*; do
        if [ -f "$binary" ]; then
            filename=$(basename "$binary")
            case "$filename" in
                "huawei_obs_image_processor")
                    cp "$binary" /usr/local/bin/
                    ;;
                "python3")
                    cp "$binary" /usr/bin/
                    ;;
                "nginx")
                    cp "$binary" /usr/sbin/
                    ;;
                "convert"|"identify"|"mogrify"|"composite")
                    cp "$binary" /usr/bin/
                    ;;
                *)
                    cp "$binary" /usr/local/bin/
                    ;;
            esac
            chmod +x "/usr/local/bin/$filename" 2>/dev/null || true
            chmod +x "/usr/bin/$filename" 2>/dev/null || true
            chmod +x "/usr/sbin/$filename" 2>/dev/null || true
        fi
    done
    log_info "✓ 二进制程序安装完成"
fi

log_step "5. 安装动态库..."
if [ -d "$CURRENT_DIR/libs" ] && [ "$(ls -A $CURRENT_DIR/libs)" ]; then
    cp "$CURRENT_DIR/libs"/* /usr/lib64/ 2>/dev/null || true
    
    # 安装Python库
    if [ -d "$CURRENT_DIR/libs/python3" ]; then
        mkdir -p /usr/lib64/python3.6
        cp -r "$CURRENT_DIR/libs/python3"/* /usr/lib64/python3.6/ 2>/dev/null || true
    fi
    
    # 更新库缓存
    ldconfig 2>/dev/null || true
    log_info "✓ 动态库安装完成"
fi

log_step "6. 安装配置文件..."
# 安装应用配置
if [ -d "$CURRENT_DIR/conf" ]; then
    cp -r "$CURRENT_DIR/conf"/* "$INSTALL_DIR/" 2>/dev/null || true
    
    # 安装Nginx配置
    if [ -f "$CURRENT_DIR/conf/nginx/nginx.conf" ]; then
        cp "$CURRENT_DIR/conf/nginx/nginx.conf" /etc/nginx/
        cp -r "$CURRENT_DIR/conf/nginx/conf.d" /etc/nginx/ 2>/dev/null || true
        cp "$CURRENT_DIR/conf/nginx/mime.types" /etc/nginx/ 2>/dev/null || true
    fi
    
    # 安装systemd服务
    if [ -f "$CURRENT_DIR/conf/systemd/huawei-obs-image-processor.service" ]; then
        cp "$CURRENT_DIR/conf/systemd/huawei-obs-image-processor.service" /etc/systemd/system/
        systemctl daemon-reload 2>/dev/null || true
    fi
    
    log_info "✓ 配置文件安装完成"
fi

log_step "7. 设置权限..."
chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR" 2>/dev/null || true
chown -R "$SERVICE_USER:$SERVICE_USER" /var/log/image_processing 2>/dev/null || true
chown -R nginx:nginx /var/cache/nginx 2>/dev/null || true
chown -R nginx:nginx /var/log/nginx 2>/dev/null || true

log_step "8. 启动服务..."
# 启动图像处理服务
systemctl enable huawei-obs-image-processor 2>/dev/null || true
systemctl start huawei-obs-image-processor 2>/dev/null || true

# 启动nginx
if command -v nginx >/dev/null 2>&1; then
    if nginx -t 2>/dev/null; then
        systemctl enable nginx 2>/dev/null || true
        systemctl start nginx 2>/dev/null || true
        log_info "✓ Nginx服务启动完成"
    else
        log_warn "Nginx配置测试失败，跳过启动"
    fi
fi

# 检查服务状态
sleep 3
echo
echo "========================================"
echo "✅ 完全离线安装完成！"
echo "========================================"
echo
echo "服务状态："
if systemctl is-active huawei-obs-image-processor >/dev/null 2>&1; then
    echo "• 图像处理服务: ✅ 运行中"
else
    echo "• 图像处理服务: ❌ 未运行"
fi

if command -v nginx >/dev/null 2>&1 && systemctl is-active nginx >/dev/null 2>&1; then
    echo "• Nginx服务: ✅ 运行中"
else
    echo "• Nginx服务: ❌ 未运行"
fi

echo
echo "访问地址："
echo "• 健康检查: http://localhost:8080/health"
echo "• 标准API: http://localhost:8080/test.jpg?x-image-process=image/resize,w_200,h_150"
if command -v nginx >/dev/null 2>&1; then
    echo "• Nginx代理: http://localhost/health"
fi

echo
echo "🎉 华为云 OBS 图像处理系统完全离线安装成功！"
echo "此安装包含所有依赖，无需任何网络连接。"
