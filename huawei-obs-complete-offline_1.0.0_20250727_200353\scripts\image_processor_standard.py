#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
华为云 OBS 图像处理服务 - 标准URL格式支持
支持 x-image-process 和 x-oss-process 参数格式
"""

import os
import sys
import json
import subprocess
import tempfile
import configparser
import logging
import base64
import re
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs, unquote
import socketserver

# 配置文件路径
CONFIG_FILE = os.environ.get('CONFIG_FILE', '/opt/huawei_obs_image_processing/conf/production.conf')

class ImageProcessorStandard:
    """标准图像处理器"""
    
    def __init__(self):
        self.setup_logging()
        self.load_config()
        self.logger.info("标准图像处理服务初始化完成")
    
    def setup_logging(self):
        """设置日志"""
        log_dir = "/var/log/image_processing"
        os.makedirs(log_dir, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{log_dir}/app.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger('ImageProcessorStandard')
    
    def load_config(self):
        """加载配置"""
        self.config = configparser.ConfigParser()
        
        if not os.path.exists(CONFIG_FILE):
            os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)
            with open(CONFIG_FILE, 'w') as f:
                f.write("""[server]
port = 8080
host = 0.0.0.0
workers = 4

[obs]
access_key_id = YOUR_ACCESS_KEY
secret_access_key = YOUR_SECRET_KEY
endpoint = obs.cn-north-4.myhuaweicloud.com
region = cn-north-4

[cache]
enable = true
max_size = 1000
ttl = 3600

[log]
level = INFO
file = /var/log/image_processing/app.log
""")
        
        self.config.read(CONFIG_FILE)
        self.logger.info(f"配置文件已加载: {CONFIG_FILE}")
    
    def parse_image_process_params(self, process_string):
        """解析图像处理参数字符串"""
        operations = []
        
        if not process_string:
            return operations
        
        # 移除 "image/" 前缀
        if process_string.startswith('image/'):
            process_string = process_string[6:]
        
        # 按 "/" 分割参数
        params = process_string.split('/')
        
        for param in params:
            if not param:
                continue
            
            # 解析每个参数
            if ',' in param:
                # 带参数的操作，如 resize,w_100,h_200
                parts = param.split(',')
                operation_type = parts[0]
                operation_params = {}
                
                for part in parts[1:]:
                    if '_' in part:
                        key, value = part.split('_', 1)
                        operation_params[key] = value
                    else:
                        # 处理没有下划线的参数，如 rotate,90
                        operation_params['value'] = part
                
                operations.append({
                    'type': operation_type,
                    'params': operation_params
                })
            else:
                # 无参数的操作，如 info
                operations.append({
                    'type': param,
                    'params': {}
                })
        
        return operations
    
    def decode_base64_text(self, encoded_text):
        """解码Base64编码的文本"""
        try:
            # URL安全的Base64解码
            decoded_bytes = base64.urlsafe_b64decode(encoded_text + '==')
            return decoded_bytes.decode('utf-8')
        except Exception as e:
            self.logger.error(f"Base64解码失败: {e}")
            return encoded_text
    
    def process_image(self, input_path, output_path, operations):
        """处理图像"""
        cmd = ['convert', input_path]
        output_format = None
        
        for operation in operations:
            op_type = operation['type']
            params = operation['params']
            
            if op_type == 'format':
                # 格式转换
                output_format = params.get('value', 'jpg')
                
            elif op_type == 'resize':
                # 缩放
                w = params.get('w')
                h = params.get('h')
                l = params.get('l')  # 最长边
                s = params.get('s')  # 最短边
                p = params.get('p')  # 百分比
                m = params.get('m', 'lfit')  # 缩放模式
                
                if p:
                    # 百分比缩放
                    cmd.extend(['-resize', f'{p}%'])
                elif w and h:
                    if m == 'lfit':
                        cmd.extend(['-resize', f'{w}x{h}'])
                    elif m == 'mfit':
                        cmd.extend(['-resize', f'{w}x{h}^'])
                    elif m == 'fill':
                        cmd.extend(['-resize', f'{w}x{h}^', '-gravity', 'center', '-crop', f'{w}x{h}+0+0'])
                    elif m == 'pad':
                        color = params.get('color', 'FFFFFF')
                        cmd.extend(['-resize', f'{w}x{h}', '-background', f'#{color}', '-gravity', 'center', '-extent', f'{w}x{h}'])
                    elif m == 'fixed':
                        cmd.extend(['-resize', f'{w}x{h}!'])
                elif w:
                    cmd.extend(['-resize', f'{w}x'])
                elif h:
                    cmd.extend(['-resize', f'x{h}'])
                elif l:
                    cmd.extend(['-resize', f'{l}x{l}>'])
                elif s:
                    cmd.extend(['-resize', f'{s}x{s}<'])
                    
            elif op_type == 'watermark':
                # 水印
                text = params.get('text', '')
                if text:
                    # 解码Base64文本
                    decoded_text = self.decode_base64_text(text)
                    color = params.get('color', '000000')
                    size = params.get('size', '40')
                    g = params.get('g', 'se')  # 位置
                    x = params.get('x', '10')
                    y = params.get('y', '10')
                    t = params.get('t', '100')  # 透明度
                    rotate = params.get('rotate', '0')
                    
                    # 位置映射
                    gravity_map = {
                        'nw': 'northwest', 'north': 'north', 'ne': 'northeast',
                        'west': 'west', 'center': 'center', 'east': 'east',
                        'sw': 'southwest', 'south': 'south', 'se': 'southeast'
                    }
                    gravity = gravity_map.get(g, 'southeast')
                    
                    # 计算透明度
                    alpha = int(t) / 100.0
                    
                    cmd.extend([
                        '-pointsize', size,
                        '-fill', f'#{color}',
                        '-gravity', gravity,
                        '-annotate', f'+{x}+{y}', decoded_text
                    ])
                    
            elif op_type == 'quality' or op_type == 'q':
                # 质量压缩
                q_value = params.get('q', params.get('value', '80'))
                cmd.extend(['-quality', q_value])
                
            elif op_type == 'rotate':
                # 旋转
                angle = params.get('value', '0')
                cmd.extend(['-rotate', angle])
                
            elif op_type == 'blur':
                # 模糊
                r = params.get('r', '1')
                s = params.get('s', '1')
                cmd.extend(['-blur', f'{r}x{s}'])
                
            elif op_type == 'crop':
                # 裁剪
                w = params.get('w')
                h = params.get('h')
                x = params.get('x', '0')
                y = params.get('y', '0')
                g = params.get('g')
                
                if w and h:
                    if g:
                        gravity_map = {
                            'nw': 'northwest', 'north': 'north', 'ne': 'northeast',
                            'west': 'west', 'center': 'center', 'east': 'east',
                            'sw': 'southwest', 'south': 'south', 'se': 'southeast'
                        }
                        gravity = gravity_map.get(g, 'northwest')
                        cmd.extend(['-gravity', gravity])
                    
                    cmd.extend(['-crop', f'{w}x{h}+{x}+{y}'])
                    
            elif op_type == 'circle':
                # 内切圆 - 简化版本
                r = params.get('r', '100')
                # 使用简单的圆形遮罩
                cmd.extend(['-alpha', 'set', '-virtual-pixel', 'transparent',
                           '-channel', 'A', '-blur', '0x1', '-level', '50,100%'])

            elif op_type == 'rounded-corners':
                # 圆角矩形 - 简化版本
                r = params.get('r', '10')
                # 使用简单的圆角效果
                cmd.extend(['-alpha', 'set', '-virtual-pixel', 'transparent'])
                
            elif op_type == 'indexcrop':
                # 索引切割
                x_size = params.get('x')
                y_size = params.get('y')
                i = params.get('i', '0')
                
                if x_size:
                    cmd.extend(['-crop', f'{x_size}x+0+0'])
                elif y_size:
                    cmd.extend(['-crop', f'x{y_size}+0+0'])
        
        # 设置输出格式
        if output_format:
            base_name = os.path.splitext(output_path)[0]
            output_path = f"{base_name}.{output_format}"
        
        cmd.append(output_path)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                self.logger.info(f"图像处理成功: {' '.join(cmd)}")
                return output_path
            else:
                self.logger.error(f"图像处理失败: {result.stderr}")
                return None
        except Exception as e:
            self.logger.error(f"图像处理异常: {e}")
            return None
    
    def get_image_info(self, image_path):
        """获取图像信息"""
        try:
            result = subprocess.run(['identify', '-format', 
                                   '{"width":%w,"height":%h,"format":"%m","size":%b,"colorspace":"%[colorspace]"}',
                                   image_path], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return json.loads(result.stdout.strip())
            else:
                return {"error": "Failed to get image info"}
        except Exception as e:
            return {"error": str(e)}

class StandardImageHandler(BaseHTTPRequestHandler):
    """标准图像处理HTTP处理器"""
    
    def __init__(self, *args, processor=None, **kwargs):
        self.processor = processor
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """重写日志方法"""
        self.processor.logger.info(f"{self.address_string()} - {format % args}")
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            if path == '/health':
                self.send_health_check()
            elif path == '/':
                self.send_welcome_page()
            else:
                # 检查是否有图像处理参数
                process_param = None
                if 'x-image-process' in query_params:
                    process_param = query_params['x-image-process'][0]
                elif 'x-oss-process' in query_params:
                    process_param = query_params['x-oss-process'][0]
                
                if process_param:
                    self.process_standard_image_request(path, process_param)
                else:
                    self.send_error(400, "Missing x-image-process or x-oss-process parameter")
                
        except Exception as e:
            self.processor.logger.error(f"处理GET请求失败: {e}")
            self.send_error(500, "Internal Server Error")
    
    def send_health_check(self):
        """健康检查"""
        status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'service': 'huawei-obs-image-processor-standard',
            'version': '2.0.0',
            'supported_formats': ['jpg', 'png', 'gif', 'webp', 'bmp', 'tiff'],
            'supported_operations': [
                'format', 'resize', 'watermark', 'quality', 'rotate', 
                'blur', 'crop', 'circle', 'rounded-corners', 'indexcrop', 'info'
            ],
            'imagemagick': self.check_imagemagick(),
            'config_file': CONFIG_FILE
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(status, indent=2).encode())
    
    def send_welcome_page(self):
        """发送欢迎页面"""
        html = """<!DOCTYPE html>
<html>
<head>
    <title>华为云 OBS 图像处理服务 - 标准版</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .status { color: green; font-weight: bold; }
        .api { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        code { background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
        .example { background: #e7f3ff; padding: 10px; margin: 5px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 华为云 OBS 图像处理服务 - 标准版</h1>
        <p class="status">✅ 服务运行正常 - 支持标准URL格式</p>
        
        <h2>📡 标准API格式</h2>
        <div class="api">
            <strong>基础格式:</strong><br>
            <code>GET /path/image.jpg?x-image-process=image/operation1/operation2/...</code><br>
            <code>GET /path/image.jpg?x-oss-process=image/operation1/operation2/...</code>
        </div>
        
        <h2>🎯 支持的操作</h2>
        
        <h3>格式转换</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/format,png</code><br>
            支持: jpg, png, webp, bmp, gif, tiff
        </div>
        
        <h3>缩放</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/resize,w_200,h_150</code><br>
            <code>/image.jpg?x-image-process=image/resize,p_50</code> (50%缩放)<br>
            <code>/image.jpg?x-image-process=image/resize,w_200,h_150,m_fill</code>
        </div>
        
        <h3>水印</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/watermark,text_SGVsbG8gV29ybGQ,color_FF0000</code><br>
            注意: 文本需要Base64编码
        </div>
        
        <h3>质量压缩</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/quality,q_80</code>
        </div>
        
        <h3>旋转</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/rotate,90</code>
        </div>
        
        <h3>裁剪</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/crop,w_200,h_150,x_10,y_10</code>
        </div>
        
        <h3>组合操作</h3>
        <div class="example">
            <code>/image.jpg?x-image-process=image/resize,w_300,h_200/watermark,text_SGVsbG8,color_000000/format,png</code>
        </div>
        
        <h2>🔗 测试链接</h2>
        <ul>
            <li><a href="/health">健康检查</a></li>
            <li><a href="/test.jpg?x-image-process=image/resize,w_200,h_150">缩放测试</a></li>
            <li><a href="/test.jpg?x-image-process=image/format,png">格式转换测试</a></li>
        </ul>
    </div>
</body>
</html>"""
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def check_imagemagick(self):
        """检查ImageMagick是否可用"""
        try:
            result = subprocess.run(['convert', '-version'], capture_output=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def process_standard_image_request(self, path, process_param):
        """处理标准图像请求"""
        # 解析操作参数
        operations = self.processor.parse_image_process_params(process_param)
        
        # 检查是否是信息查询
        info_operation = next((op for op in operations if op['type'] == 'info'), None)
        if info_operation:
            self.handle_image_info_request(path)
            return
        
        # 处理图像
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, 'input.png')
            output_path = os.path.join(temp_dir, 'output.png')
            
            # 创建示例图像（实际应用中应该从OBS下载）
            self.create_sample_image(input_path, path)
            
            # 处理图像
            result_path = self.processor.process_image(input_path, output_path, operations)
            
            if result_path and os.path.exists(result_path):
                self.send_image_response(result_path)
            else:
                self.send_error(500, "Image processing failed")
    
    def create_sample_image(self, output_path, original_path):
        """创建示例图像"""
        image_name = os.path.basename(original_path)
        cmd = [
            'convert', '-size', '400x300', 'xc:lightblue',
            '-pointsize', '20', '-fill', 'black',
            '-gravity', 'center', '-annotate', '+0+0', f'Sample: {image_name}',
            output_path
        ]
        
        try:
            subprocess.run(cmd, check=True, timeout=10)
        except Exception as e:
            self.processor.logger.error(f"创建示例图像失败: {e}")
    
    def handle_image_info_request(self, path):
        """处理图像信息请求"""
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, 'input.png')
            self.create_sample_image(input_path, path)
            
            info = self.processor.get_image_info(input_path)
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(info, indent=2).encode())
    
    def send_image_response(self, image_path):
        """发送图像响应"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 确定MIME类型
            _, ext = os.path.splitext(image_path)
            mime_type = {
                '.jpg': 'image/jpeg', '.jpeg': 'image/jpeg',
                '.png': 'image/png', '.gif': 'image/gif',
                '.webp': 'image/webp', '.bmp': 'image/bmp',
                '.tiff': 'image/tiff'
            }.get(ext.lower(), 'application/octet-stream')
            
            self.send_response(200)
            self.send_header('Content-Type', mime_type)
            self.send_header('Content-Length', str(len(image_data)))
            self.send_header('Cache-Control', 'public, max-age=3600')
            self.send_header('Content-Disposition', 'attachment')
            self.end_headers()
            self.wfile.write(image_data)
            
        except Exception as e:
            self.processor.logger.error(f"发送图像响应失败: {e}")
            self.send_error(500, "Failed to send image")

def main():
    """主函数"""
    processor = ImageProcessorStandard()
    
    # 读取配置
    host = processor.config.get('server', 'host', fallback='0.0.0.0')
    port = int(processor.config.get('server', 'port', fallback=8080))
    
    # 创建HTTP服务器
    def handler(*args, **kwargs):
        return StandardImageHandler(*args, processor=processor, **kwargs)
    
    # 使用ThreadingTCPServer支持并发
    class ThreadedHTTPServer(socketserver.ThreadingMixIn, HTTPServer):
        daemon_threads = True
    
    server = ThreadedHTTPServer((host, port), handler)
    
    processor.logger.info(f"华为云 OBS 图像处理服务 - 标准版启动")
    processor.logger.info(f"监听地址: {host}:{port}")
    processor.logger.info(f"支持标准URL格式: x-image-process 和 x-oss-process")
    processor.logger.info(f"访问地址: http://{host}:{port}/")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        processor.logger.info("服务停止")
        server.shutdown()

if __name__ == '__main__':
    main()
