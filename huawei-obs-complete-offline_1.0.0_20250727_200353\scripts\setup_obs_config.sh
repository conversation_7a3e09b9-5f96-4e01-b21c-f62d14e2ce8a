#!/bin/bash

# 华为云 OBS 配置向导

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

CONFIG_FILE="/opt/huawei_obs_image_processing/conf/production.conf"

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
   log_error "此脚本需要root权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

echo "========================================"
echo "🔧 华为云 OBS 配置向导"
echo "========================================"
echo

log_step "检查配置文件..."
if [ ! -f "$CONFIG_FILE" ]; then
    log_error "配置文件不存在: $CONFIG_FILE"
    exit 1
fi

log_info "当前配置文件: $CONFIG_FILE"
echo

log_step "显示当前OBS配置..."
echo "当前配置:"
echo "----------------------------------------"
grep -A 10 "\[obs\]" "$CONFIG_FILE"
echo "----------------------------------------"
echo

echo "华为云 OBS 配置说明:"
echo "1. 访问密钥ID (Access Key ID): 华为云账户的访问密钥"
echo "2. 秘密访问密钥 (Secret Access Key): 对应的秘密密钥"
echo "3. 终端节点 (Endpoint): OBS服务的访问地址"
echo "4. 区域 (Region): OBS桶所在的区域"
echo

echo "常用华为云OBS终端节点:"
echo "• 华北-北京四: obs.cn-north-4.myhuaweicloud.com"
echo "• 华东-上海一: obs.cn-east-3.myhuaweicloud.com"
echo "• 华南-广州: obs.cn-south-1.myhuaweicloud.com"
echo "• 华东-上海二: obs.cn-east-2.myhuaweicloud.com"
echo

read -p "是否要配置华为云 OBS 设置? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "配置取消"
    exit 0
fi

log_step "配置华为云 OBS..."

# 备份原配置
backup_file="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
cp "$CONFIG_FILE" "$backup_file"
log_info "配置文件已备份到: $backup_file"

# 获取用户输入
echo
echo "请输入华为云 OBS 配置信息:"
echo

read -p "访问密钥ID (Access Key ID): " access_key_id
if [ -z "$access_key_id" ]; then
    log_error "访问密钥ID不能为空"
    exit 1
fi

read -s -p "秘密访问密钥 (Secret Access Key): " secret_access_key
echo
if [ -z "$secret_access_key" ]; then
    log_error "秘密访问密钥不能为空"
    exit 1
fi

echo
echo "选择区域:"
echo "1) 华北-北京四 (cn-north-4)"
echo "2) 华东-上海一 (cn-east-3)"
echo "3) 华南-广州 (cn-south-1)"
echo "4) 华东-上海二 (cn-east-2)"
echo "5) 自定义"

read -p "请选择区域 (1-5): " region_choice

case $region_choice in
    1)
        region="cn-north-4"
        endpoint="obs.cn-north-4.myhuaweicloud.com"
        ;;
    2)
        region="cn-east-3"
        endpoint="obs.cn-east-3.myhuaweicloud.com"
        ;;
    3)
        region="cn-south-1"
        endpoint="obs.cn-south-1.myhuaweicloud.com"
        ;;
    4)
        region="cn-east-2"
        endpoint="obs.cn-east-2.myhuaweicloud.com"
        ;;
    5)
        read -p "请输入区域代码 (如: cn-north-4): " region
        read -p "请输入终端节点 (如: obs.cn-north-4.myhuaweicloud.com): " endpoint
        ;;
    *)
        log_error "无效选择"
        exit 1
        ;;
esac

read -p "默认存储桶名称 (可选): " bucket_name

log_step "更新配置文件..."

# 使用sed更新配置文件
sed -i "s/^access_key_id = .*/access_key_id = $access_key_id/" "$CONFIG_FILE"
sed -i "s/^secret_access_key = .*/secret_access_key = $secret_access_key/" "$CONFIG_FILE"
sed -i "s/^endpoint = .*/endpoint = $endpoint/" "$CONFIG_FILE"
sed -i "s/^region = .*/region = $region/" "$CONFIG_FILE"

# 如果提供了桶名称，添加到配置中
if [ -n "$bucket_name" ]; then
    if grep -q "^bucket = " "$CONFIG_FILE"; then
        sed -i "s/^bucket = .*/bucket = $bucket_name/" "$CONFIG_FILE"
    else
        sed -i "/^\[obs\]/a bucket = $bucket_name" "$CONFIG_FILE"
    fi
fi

log_info "✓ 配置文件更新完成"

log_step "验证配置..."
echo "更新后的配置:"
echo "----------------------------------------"
grep -A 10 "\[obs\]" "$CONFIG_FILE" | sed 's/secret_access_key = .*/secret_access_key = [HIDDEN]/'
echo "----------------------------------------"

log_step "安装Python依赖..."
# 检查是否需要安装boto3
if command -v python3 >/dev/null 2>&1; then
    log_info "检查boto3安装状态..."
    if python3 -c "import boto3" 2>/dev/null; then
        log_info "✓ boto3 已安装"
    else
        log_warn "boto3 未安装，尝试安装..."
        
        # 尝试使用pip安装
        if command -v pip3 >/dev/null 2>&1; then
            pip3 install boto3 || log_warn "pip3安装失败"
        elif command -v yum >/dev/null 2>&1; then
            yum install -y python3-pip && pip3 install boto3 || log_warn "yum安装失败"
        else
            log_warn "无法自动安装boto3，请手动安装: pip3 install boto3"
        fi
    fi
else
    log_warn "Python3 未安装，无法使用OBS功能"
fi

log_step "测试OBS连接..."
if command -v python3 >/dev/null 2>&1 && python3 -c "import boto3" 2>/dev/null; then
    log_info "创建连接测试脚本..."
    
    cat > /tmp/test_obs_connection.py << EOF
#!/usr/bin/env python3
import boto3
import configparser
from botocore.exceptions import ClientError

config = configparser.ConfigParser()
config.read('$CONFIG_FILE')

try:
    access_key = config.get('obs', 'access_key_id')
    secret_key = config.get('obs', 'secret_access_key')
    endpoint = config.get('obs', 'endpoint')
    region = config.get('obs', 'region')
    
    client = boto3.client(
        's3',
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        endpoint_url=f'https://{endpoint}',
        region_name=region
    )
    
    # 测试连接
    response = client.list_buckets()
    print("✓ OBS连接成功!")
    print(f"账户下的存储桶数量: {len(response['Buckets'])}")
    
    for bucket in response['Buckets']:
        print(f"  - {bucket['Name']} (创建时间: {bucket['CreationDate']})")
        
except Exception as e:
    print(f"✗ OBS连接失败: {e}")
    exit(1)
EOF

    log_info "执行连接测试..."
    if python3 /tmp/test_obs_connection.py; then
        log_info "✓ OBS配置验证成功"
    else
        log_error "✗ OBS配置验证失败，请检查配置信息"
    fi
    
    rm -f /tmp/test_obs_connection.py
else
    log_warn "跳过OBS连接测试（缺少依赖）"
fi

log_step "重启图像处理服务..."
SERVICE_NAME="huawei-obs-image-processor"
if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
    log_info "重启服务以应用新配置..."
    systemctl restart "$SERVICE_NAME"
    sleep 3
    
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        log_info "✓ 服务重启成功"
    else
        log_error "✗ 服务重启失败"
        systemctl status "$SERVICE_NAME"
    fi
else
    log_info "启动服务..."
    systemctl start "$SERVICE_NAME"
    sleep 3
    
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        log_info "✓ 服务启动成功"
    else
        log_error "✗ 服务启动失败"
        systemctl status "$SERVICE_NAME"
    fi
fi

echo
echo "========================================"
echo "✅ OBS 配置完成！"
echo "========================================"
echo
echo "配置信息："
echo "• 配置文件: $CONFIG_FILE"
echo "• 备份文件: $backup_file"
echo "• 区域: $region"
echo "• 终端节点: $endpoint"
if [ -n "$bucket_name" ]; then
    echo "• 默认存储桶: $bucket_name"
fi
echo
echo "下一步操作："
echo "1. 测试图像处理: ./test_complete_system.sh"
echo "2. 查看服务状态: systemctl status $SERVICE_NAME"
echo "3. 查看服务日志: journalctl -u $SERVICE_NAME -f"
echo
echo "API使用示例："
echo "• 健康检查: curl http://localhost:8080/health"
echo "• 图像处理: curl 'http://localhost:8080/images/sample.jpg?w=200&h=150'"
echo "• 添加水印: curl 'http://localhost:8080/images/sample.jpg?watermark=Copyright'"
echo
echo "🎉 华为云 OBS 配置向导完成！"
