#!/bin/bash

# 标准API格式测试脚本

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[TEST]${NC} $1"; }

echo "========================================"
echo "🧪 标准API格式功能测试"
echo "   验证 x-image-process 和 x-oss-process"
echo "========================================"
echo

BASE_URL="http://localhost:8080"
TEST_DIR="/tmp/standard_api_test_$(date +%s)"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

log_step "1. 基础连接测试..."
if command -v curl >/dev/null 2>&1; then
    # 健康检查
    if curl -s --connect-timeout 10 "$BASE_URL/health" >/dev/null; then
        log_info "✅ 服务连接正常"
    else
        log_error "❌ 服务连接失败"
        exit 1
    fi
else
    log_error "curl未安装，无法进行测试"
    exit 1
fi

log_step "2. 测试格式转换..."
formats=("png" "jpg" "webp" "bmp" "gif")
for format in "${formats[@]}"; do
    log_info "测试转换为 $format 格式..."
    url="$BASE_URL/test.jpg?x-image-process=image/format,$format"
    
    if curl -s --connect-timeout 15 "$url" -o "format_test.$format" 2>/dev/null; then
        if [ -f "format_test.$format" ] && [ -s "format_test.$format" ]; then
            size=$(stat -c%s "format_test.$format")
            log_info "  ✅ $format 格式转换成功 (${size} bytes)"
        else
            log_warn "  ❌ $format 格式转换失败"
        fi
    else
        log_warn "  ❌ $format 格式请求失败"
    fi
done

log_step "3. 测试缩放功能..."
resize_tests=(
    "resize,w_200,h_150:固定宽高"
    "resize,w_300:固定宽度"
    "resize,h_200:固定高度"
    "resize,p_50:50%缩放"
    "resize,w_200,h_150,m_fill:填充模式"
    "resize,w_200,h_150,m_pad:填充模式"
)

for test in "${resize_tests[@]}"; do
    IFS=':' read -r params description <<< "$test"
    log_info "测试: $description..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/$params"
    output_file="resize_$(echo $params | tr ',' '_').png"
    
    if curl -s --connect-timeout 15 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ $description 成功 (${size} bytes)"
        else
            log_warn "  ❌ $description 失败"
        fi
    else
        log_warn "  ❌ $description 请求失败"
    fi
done

log_step "4. 测试水印功能..."
# Base64编码测试文本
test_text="Hello World"
encoded_text=$(echo -n "$test_text" | base64 -w 0 | tr '+/' '-_' | tr -d '=')

watermark_tests=(
    "watermark,text_$encoded_text,color_FF0000:红色水印"
    "watermark,text_$encoded_text,color_000000,size_30:大号黑色水印"
    "watermark,text_$encoded_text,color_0000FF,g_center:居中蓝色水印"
)

for test in "${watermark_tests[@]}"; do
    IFS=':' read -r params description <<< "$test"
    log_info "测试: $description..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/$params"
    output_file="watermark_$(echo $params | tr ',' '_' | cut -c1-20).png"
    
    if curl -s --connect-timeout 15 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ $description 成功 (${size} bytes)"
        else
            log_warn "  ❌ $description 失败"
        fi
    else
        log_warn "  ❌ $description 请求失败"
    fi
done

log_step "5. 测试质量压缩..."
quality_tests=("90" "70" "50" "30")
for quality in "${quality_tests[@]}"; do
    log_info "测试质量 ${quality}%..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/quality,q_$quality"
    output_file="quality_$quality.jpg"
    
    if curl -s --connect-timeout 15 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ 质量${quality}% 成功 (${size} bytes)"
        else
            log_warn "  ❌ 质量${quality}% 失败"
        fi
    else
        log_warn "  ❌ 质量${quality}% 请求失败"
    fi
done

log_step "6. 测试旋转功能..."
rotate_tests=("90" "180" "270")
for angle in "${rotate_tests[@]}"; do
    log_info "测试旋转 ${angle}度..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/rotate,$angle"
    output_file="rotate_$angle.png"
    
    if curl -s --connect-timeout 15 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ 旋转${angle}度 成功 (${size} bytes)"
        else
            log_warn "  ❌ 旋转${angle}度 失败"
        fi
    else
        log_warn "  ❌ 旋转${angle}度 请求失败"
    fi
done

log_step "7. 测试裁剪功能..."
crop_tests=(
    "crop,w_200,h_150:基础裁剪"
    "crop,w_200,h_150,x_50,y_50:偏移裁剪"
    "crop,w_200,h_150,g_center:居中裁剪"
)

for test in "${crop_tests[@]}"; do
    IFS=':' read -r params description <<< "$test"
    log_info "测试: $description..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/$params"
    output_file="crop_$(echo $params | tr ',' '_').png"
    
    if curl -s --connect-timeout 15 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ $description 成功 (${size} bytes)"
        else
            log_warn "  ❌ $description 失败"
        fi
    else
        log_warn "  ❌ $description 请求失败"
    fi
done

log_step "8. 测试组合操作..."
combo_tests=(
    "resize,w_300,h_200/format,png:缩放+格式转换"
    "resize,w_250,h_200/watermark,text_$encoded_text,color_FF0000:缩放+水印"
    "resize,w_300,h_200/quality,q_80/format,jpg:缩放+质量+格式"
    "watermark,text_$encoded_text,color_000000/rotate,90/format,png:水印+旋转+格式"
)

for test in "${combo_tests[@]}"; do
    IFS=':' read -r params description <<< "$test"
    log_info "测试: $description..."
    
    url="$BASE_URL/test.jpg?x-image-process=image/$params"
    output_file="combo_$(echo $params | tr '/' '_' | tr ',' '_' | cut -c1-30).png"
    
    if curl -s --connect-timeout 20 "$url" -o "$output_file" 2>/dev/null; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            size=$(stat -c%s "$output_file")
            log_info "  ✅ $description 成功 (${size} bytes)"
        else
            log_warn "  ❌ $description 失败"
        fi
    else
        log_warn "  ❌ $description 请求失败"
    fi
done

log_step "9. 测试 x-oss-process 参数..."
log_info "测试 x-oss-process 格式..."
url="$BASE_URL/test.jpg?x-oss-process=image/resize,w_200,h_150/format,png"
if curl -s --connect-timeout 15 "$url" -o "oss_process_test.png" 2>/dev/null; then
    if [ -f "oss_process_test.png" ] && [ -s "oss_process_test.png" ]; then
        size=$(stat -c%s "oss_process_test.png")
        log_info "  ✅ x-oss-process 格式支持正常 (${size} bytes)"
    else
        log_warn "  ❌ x-oss-process 格式失败"
    fi
else
    log_warn "  ❌ x-oss-process 请求失败"
fi

log_step "10. 测试图像信息获取..."
url="$BASE_URL/test.jpg?x-image-process=image/info"
if curl -s --connect-timeout 10 "$url" -o "image_info.json" 2>/dev/null; then
    if [ -f "image_info.json" ] && [ -s "image_info.json" ]; then
        log_info "✅ 图像信息获取成功"
        echo "图像信息:"
        cat "image_info.json" | python3 -m json.tool 2>/dev/null || cat "image_info.json"
    else
        log_warn "❌ 图像信息获取失败"
    fi
else
    log_warn "❌ 图像信息请求失败"
fi

log_step "11. 性能测试..."
log_info "执行并发性能测试..."
start_time=$(date +%s.%N)

# 并发执行10个请求
for i in {1..10}; do
    (
        curl -s "$BASE_URL/test.jpg?x-image-process=image/resize,w_100,h_100" -o "perf_$i.png" 2>/dev/null
    ) &
done

# 等待所有请求完成
wait

end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "N/A")

if [ "$duration" != "N/A" ]; then
    log_info "并发测试完成: ${duration}秒"
    success_count=$(ls perf_*.png 2>/dev/null | wc -l)
    log_info "成功处理: $success_count/10 个请求"
else
    log_info "并发测试完成"
fi

echo
echo "========================================"
echo "📊 标准API测试结果汇总"
echo "========================================"

# 统计生成的文件
total_files=$(ls -1 *.png *.jpg *.webp *.bmp *.gif *.json 2>/dev/null | wc -l)
echo "生成的文件总数: $total_files"

echo
echo "文件大小统计:"
echo "----------------------------------------"
for file in format_test.* resize_*.* watermark_*.* quality_*.* rotate_*.* crop_*.* combo_*.* oss_process_test.* perf_*.* image_info.json; do
    if [ -f "$file" ]; then
        size=$(stat -c%s "$file" 2>/dev/null || echo "0")
        printf "%-30s %8s bytes\n" "$file" "$size"
    fi
done | head -20
echo "----------------------------------------"

echo
echo "功能支持检查:"
tests=(
    "格式转换:$(ls format_test.* 2>/dev/null | wc -l)/5"
    "缩放功能:$(ls resize_*.* 2>/dev/null | wc -l)/6"
    "水印功能:$(ls watermark_*.* 2>/dev/null | wc -l)/3"
    "质量压缩:$(ls quality_*.* 2>/dev/null | wc -l)/4"
    "旋转功能:$(ls rotate_*.* 2>/dev/null | wc -l)/3"
    "裁剪功能:$(ls crop_*.* 2>/dev/null | wc -l)/3"
    "组合操作:$(ls combo_*.* 2>/dev/null | wc -l)/4"
    "OSS格式:$([ -f "oss_process_test.png" ] && echo "1/1" || echo "0/1")"
    "图像信息:$([ -f "image_info.json" ] && echo "1/1" || echo "0/1")"
)

for test in "${tests[@]}"; do
    IFS=':' read -r name result <<< "$test"
    echo "• $name: $result"
done

echo
echo "测试文件保存在: $TEST_DIR"
echo "清理命令: rm -rf $TEST_DIR"

# 清理测试文件
cd /
rm -rf "$TEST_DIR"

echo
if [ "$total_files" -gt 20 ]; then
    echo "🎉 标准API格式测试通过！所有功能正常工作。"
    echo
    echo "支持的URL格式："
    echo "• x-image-process: ✅"
    echo "• x-oss-process: ✅"
    echo "• 组合操作: ✅"
    echo "• 参数顺序无关: ✅"
    echo
    echo "现在可以使用标准的华为云OBS图像处理API格式了！"
else
    echo "⚠️ 部分功能可能有问题，请检查服务日志"
fi

echo
echo "🔧 标准API格式测试完成！"
